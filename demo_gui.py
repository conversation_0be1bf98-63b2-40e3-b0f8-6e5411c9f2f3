#!/usr/bin/env python3
"""
GUI应用程序演示脚本

这个脚本演示如何使用GUI应用程序合并Markdown文件。
它会创建示例文件，启动GUI，然后清理演示文件。
"""
import os
import time
import tempfile
import shutil
from pathlib import Path


def create_demo_files():
    """创建演示文件"""
    print("📁 创建演示文件...")
    
    # 确保演示文件存在且有不同的创建时间
    demo_files = [
        ("demo_file1.md", "# 演示文件1\n\n这是第一个文件（最早创建）。\n\n## 内容\n- 项目1\n- 项目2"),
        ("demo_file2.md", "# 演示文件2\n\n这是第二个文件（中间创建）。\n\n## 代码\n```python\nprint('Hello World')\n```"),
        ("demo_file3.md", "# 演示文件3\n\n这是第三个文件（最晚创建）。\n\n## 表格\n| 列1 | 列2 |\n|-----|-----|\n| A | B |")
    ]
    
    created_files = []
    for i, (filename, content) in enumerate(demo_files):
        if not os.path.exists(filename):
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 创建了 {filename}")
            created_files.append(filename)
            
            # 添加延迟确保文件有不同的创建时间
            if i < len(demo_files) - 1:
                time.sleep(1.2)
        else:
            print(f"  ℹ️  {filename} 已存在")
            created_files.append(filename)
    
    return created_files


def show_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("🎯 GUI使用演示")
    print("=" * 60)
    print()
    print("GUI应用程序即将启动，您可以：")
    print()
    print("1️⃣  文件添加方式：")
    print("   • 点击'添加文件'按钮选择演示文件")
    print("   • 或者拖拽文件到列表区域（如果支持拖拽）")
    print("   • 或者双击文件列表区域")
    print()
    print("2️⃣  选择演示文件：")
    print("   • demo_file1.md")
    print("   • demo_file2.md") 
    print("   • demo_file3.md")
    print()
    print("3️⃣  设置输出：")
    print("   • 保持默认输出文件名 'combined_by_time.md'")
    print("   • 或者点击'浏览'选择其他位置")
    print()
    print("4️⃣  开始合并：")
    print("   • 点击'开始合并'按钮")
    print("   • 观察进度条和日志信息")
    print("   • 合并完成后选择是否打开文件")
    print()
    print("5️⃣  验证结果：")
    print("   • 检查文件是否按创建时间排序")
    print("   • demo_file1 应该在最前面")
    print("   • demo_file3 应该在最后面")
    print()
    print("💡 提示：关闭GUI后，演示文件将被自动清理")
    print("=" * 60)


def cleanup_demo_files(files):
    """清理演示文件"""
    print("\n🧹 清理演示文件...")
    
    # 清理演示文件
    for filename in files:
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"  ✅ 删除了 {filename}")
        except Exception as e:
            print(f"  ❌ 删除 {filename} 失败: {e}")
    
    # 清理可能生成的输出文件
    output_files = ["combined_by_time.md", "demo_output.md"]
    for filename in output_files:
        try:
            if os.path.exists(filename):
                choice = input(f"是否删除输出文件 {filename}? (y/N): ").lower()
                if choice in ['y', 'yes']:
                    os.remove(filename)
                    print(f"  ✅ 删除了 {filename}")
                else:
                    print(f"  ℹ️  保留了 {filename}")
        except Exception as e:
            print(f"  ❌ 删除 {filename} 失败: {e}")


def main():
    """主函数"""
    print("🚀 Markdown文件合并器GUI - 演示模式")
    print("=" * 60)
    
    try:
        # 创建演示文件
        demo_files = create_demo_files()
        
        # 显示使用说明
        show_instructions()
        
        # 等待用户确认
        input("\n按Enter键启动GUI演示...")
        
        # 启动GUI
        print("\n🖥️  正在启动GUI应用程序...")
        try:
            from markdown_combiner_gui import main as gui_main
            gui_main()
        except KeyboardInterrupt:
            print("\n用户取消操作")
        except Exception as e:
            print(f"❌ 启动GUI失败: {e}")
            print("请尝试直接运行: python markdown_combiner_gui.py")
        
    except KeyboardInterrupt:
        print("\n用户取消演示")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
    finally:
        # 清理演示文件
        if 'demo_files' in locals():
            cleanup_demo_files(demo_files)
    
    print("\n✨ 演示完成！感谢使用Markdown文件合并器GUI")


if __name__ == "__main__":
    main()
