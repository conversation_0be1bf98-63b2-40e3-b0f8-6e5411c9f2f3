"""
安装GUI应用程序的可选依赖

这个脚本帮助安装tkinterdnd2库以启用拖拽功能。
如果安装失败，GUI应用程序仍然可以正常工作，只是没有拖拽功能。
"""
import subprocess
import sys


def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    print("Markdown文件合并器GUI - 依赖安装程序")
    print("=" * 50)
    
    # 检查当前Python版本
    print(f"Python版本: {sys.version}")
    print()
    
    # 安装可选依赖
    print("正在安装可选依赖以启用拖拽功能...")
    
    success = install_package("tkinterdnd2")
    
    print()
    print("=" * 50)
    
    if success:
        print("🎉 所有依赖安装完成！")
        print("现在您可以运行 'python markdown_combiner_gui.py' 来启动GUI应用程序。")
        print("GUI将支持完整的拖拽功能。")
    else:
        print("⚠️  可选依赖安装失败，但这不会影响主要功能。")
        print("您仍然可以运行 'python markdown_combiner_gui.py' 来启动GUI应用程序。")
        print("只是无法使用拖拽功能，需要通过按钮选择文件。")
    
    print()
    print("如果遇到问题，请尝试以下解决方案：")
    print("1. 确保您有网络连接")
    print("2. 尝试使用管理员权限运行此脚本")
    print("3. 手动运行: pip install tkinterdnd2")


if __name__ == "__main__":
    main()
