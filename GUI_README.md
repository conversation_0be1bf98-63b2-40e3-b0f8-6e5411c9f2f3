# Markdown文件合并器GUI

一个用户友好的图形界面应用程序，用于将多个Markdown文件按创建时间排序合并为单个文件。

## 功能特性

### 🎯 核心功能
- **按创建时间排序**：自动按文件创建时间从早到晚排序合并
- **拖拽支持**：支持直接拖拽.md文件到界面（需要安装可选依赖）
- **文件管理**：添加、移除、清空文件列表
- **自定义输出**：选择输出文件的位置和名称
- **实时反馈**：显示合并进度和详细日志

### 🖥️ 界面特性
- **直观操作**：简洁清晰的用户界面
- **多线程处理**：后台处理文件合并，界面不会卡顿
- **错误处理**：友好的错误提示和异常处理
- **操作日志**：详细的操作记录和状态显示

## 安装和运行

### 1. 基本运行（无拖拽功能）
```bash
# 直接运行GUI应用程序
python markdown_combiner_gui.py
```

### 2. 完整功能运行（包含拖拽）
```bash
# 首先安装可选依赖
python install_dependencies.py

# 然后运行GUI应用程序
python markdown_combiner_gui.py
```

## 使用方法

### 📁 添加文件
1. **拖拽方式**（推荐）：直接将.md文件拖拽到文件列表区域
2. **按钮方式**：点击"添加文件"按钮选择文件
3. **双击方式**：双击文件列表区域打开文件选择对话框

### ⚙️ 设置输出
1. 在"输出文件"框中输入文件名，或点击"浏览"按钮选择位置
2. 默认输出文件名为 `combined_by_time.md`

### 🚀 开始合并
1. 确保已添加至少一个.md文件
2. 点击"开始合并"按钮
3. 观察进度条和日志信息
4. 合并完成后选择是否打开输出文件

### 🗂️ 文件管理
- **移除文件**：选中文件后点击"移除选中"
- **清空列表**：点击"清空列表"移除所有文件
- **查看路径**：文件列表显示文件名和完整路径

## 界面说明

```
┌─────────────────────────────────────────────────────────────┐
│                    Markdown文件合并器                        │
├─────────────────────────────────────────────────────────────┤
│ 选择Markdown文件                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 拖拽.md文件到此处，或点击下方按钮选择文件                  │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ file1.md (/path/to/file1.md)                       │ │ │
│ │ │ file2.md (/path/to/file2.md)                       │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ [添加文件] [移除选中] [清空列表]                          │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 输出设置                                                     │
│ 输出文件: [combined_by_time.md              ] [浏览]        │
├─────────────────────────────────────────────────────────────┤
│ [开始合并] [进度条] 状态: 就绪                               │
├─────────────────────────────────────────────────────────────┤
│ 操作日志                                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 拖拽功能已启用 - 可以直接拖拽文件到列表中                │ │
│ │ 添加了 2 个文件                                          │ │
│ │ 开始合并 2 个文件...                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 技术特性

### 🔧 依赖管理
- **核心功能**：仅依赖Python标准库（tkinter）
- **拖拽功能**：可选依赖tkinterdnd2库
- **自动降级**：如果拖拽库不可用，自动禁用拖拽功能

### 🧵 多线程处理
- 文件合并在后台线程执行，避免界面冻结
- 实时更新进度和日志信息
- 线程安全的UI更新机制

### 🛡️ 错误处理
- 文件权限检查
- 编码错误处理
- 路径有效性验证
- 用户友好的错误提示

## 故障排除

### 拖拽功能不工作
1. 运行 `python install_dependencies.py` 安装依赖
2. 如果安装失败，使用按钮方式添加文件

### 合并失败
1. 检查文件权限
2. 确保输出路径有效
3. 查看操作日志中的详细错误信息

### 界面显示异常
1. 确保使用Python 3.6+
2. 检查tkinter是否正确安装
3. 尝试调整窗口大小

## 文件结构

```
├── markdown_combiner_gui.py      # GUI主程序
├── combine_markdown_by_time.py   # 核心合并逻辑
├── install_dependencies.py       # 依赖安装脚本
├── GUI_README.md                 # 本说明文件
└── test_combine_markdown_by_time.py  # 单元测试
```

## 系统要求

- **Python版本**：3.6或更高
- **操作系统**：Windows、macOS、Linux
- **必需依赖**：tkinter（通常随Python安装）
- **可选依赖**：tkinterdnd2（用于拖拽功能）

## 许可证

本项目遵循MIT许可证。
