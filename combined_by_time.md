<!-- 文件: Claude_code_要限制了，即使你是max用户.md | 创建时间: 2025-08-25 16:39:41 -->

1\. 什么事？ Claude Pro 和 Max 要设置每周用量上限了

2\. 什么时候？ 8 月 28 日开始

3\. 为什么？

\* 成本太高：有极少数用户（公告里称为“超级粉丝”）24小时不停地用，一个200美元套餐的用户能跑出几万美元的成本，公司扛不住了  
\* 有人违规：还有少数人共享、转卖账号，占用了资源，影响了大家

4\. 对我影响大吗？ 可能不大。官方估计，只有不到 5% 的用户会受到影响

5\. 用量超了怎么办？ 如果你是 Max 用户，用超了可以按 API 的标准价格加钱买

6\. 官方态度：官方表示这是为了保证服务稳定，同时欢迎“重度用户”（Power User）给他们提建议，帮助他们找到更好的服务方式。

ClaudeCode 安装教程： [https://yoo7f1r4m4.feishu.cn/wiki/WEhlwyO83iZC4WknX8CcscnEnPh](https://yoo7f1r4m4.feishu.cn/wiki/WEhlwyO83iZC4WknX8CcscnEnPh)


---

<!-- 文件: 使用Claude Code 绘制一个 3D 地球-2025-08-25 16_41_10.md | 创建时间: 2025-08-25 16:41:10 -->

# 使用Claude Code 绘制一个 3D 地球

原创 败影 败影

_2025年08月25日 07:22_ _广东_

一起来看看使用claude code 绘制出来的3D地球。

已关注

关注

重播 分享 赞

关闭

**观看更多**

更多

_退出全屏_

_切换到竖屏全屏__退出全屏_

败影已关注

分享视频

，时长00:10

0/0

00:00/00:10

切换到横屏模式

继续播放

进度条，百分之0

播放

00:00

/

00:10

00:10

_全屏_

倍速播放中

0.5倍 0.75倍 1.0倍 1.5倍 2.0倍

超清 流畅

您的浏览器不支持 video 标签

继续观看

使用Claude Code 绘制一个 3D 地球

观看更多

原创

,

使用Claude Code 绘制一个 3D 地球

败影已关注

分享点赞在看

已同步到看一看写下你的评论

视频详情

效果还是很好的。

提示词：

```
`# 角色设定`你是一位经验丰富的前端3D图形开发专家，精通
            [Three.js和WebGL技术。](http://Three.js和WebGL技术。)
          
`# 任务目标``创建一个交互式的3D地球可视化应用，具备专业级的视觉效果和流畅的用户体验。`
`# 核心功能要求``## 3D地球模型``- 使用高分辨率地球纹理贴图（建议4K分辨率）``- 实现平滑的自转动画（可调节转速）``- 添加大气层光晕效果增强真实感``- 支持昼夜交替的光照效果`
`## 交互控制``- 集成OrbitControls实现鼠标/触控旋转、缩放、平移``- 自转与手动控制的智能切换（交互时暂停自转，停止交互后恢复）``- 平滑的缓动动画过渡`
`## 视觉增强``- 创建逼真的星空背景（粒子系统或天空盒）``- 添加适当的环境光和方向光``- 实现抗锯齿和阴影效果``- 响应式设计，适配不同屏幕尺寸`
`# 技术规范`-  使用最新版
            [Three.js](http://Three.js) (r150+)`- 优化渲染性能，保持60fps``- 支持现代浏览器的WebGL 2.0``- 代码结构清晰，便于维护和扩展`
`# 输出格式``请提供完整的HTML文件，包含：``- 结构化的HTML5文档``- 内联CSS样式（全屏展示，去除边距）``- 完整的JavaScript代码（模块化组织）``- 详细的代码注释``- 基本的加载状态提示`
`# 额外加分项``- 添加地球标签或热点标记``- 实现昼夜分界线效果``- 性能监控面板（可选）``- 自适应质量设置（根据设备性能调整）`
`请确保代码具备良好的可读性和可维护性，并在关键部分添加注释说明。`
```