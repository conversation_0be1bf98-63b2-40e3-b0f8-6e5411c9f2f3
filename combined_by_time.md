<!-- 文件: Claude_Code做Vibe_Coding的双窗口工作流，真香！.md | 创建时间: 2025-08-25 16:38:34 -->

最近一直在做一个算法可视化的项目，有一个感觉：**Claude Code + 合理的窗口布局，真的可以让开发体验丝滑到飞起。**

尤其是做 **Vibe Coding**（沉浸式协同写代码）的时候，我强烈建议大家——**一定要开两个Claude Code窗口**。

## **为什么要双窗口？**

Claude Code的终端是可以无限开的，而大多数人只开一个标签页，所有事都在一个窗口里做，这就会带来两个问题：

1.  **上下文被污染**写着写着代码，Review 的信息、Debug 的信息全糅在一起，Claude 的上下文乱得像家里装修时的客厅。
2.  **角色混乱**Claude 既要帮你写代码，又要帮你排错，还要回答你的奇怪问题，这就像让一个全栈工程师同时做 PM + QA + 开发，迟早精神分裂。

## 我的做法

我会把 Claude Code 当成**两个不同的虚拟同事**：

- **窗口 A**：专职“开发牛马” 它的唯一任务就是按照需求写代码，尽快实现功能，不关心外部的抱怨和质疑。
- **窗口 B**：专职“Review & QA” 它的任务是审查代码，挑刺，记录问题，并维护两个关键文档：

1.  **Changelog**（功能更新记录）
2.  **Troubleshooting**（问题与解决方案）

两者的互动很简单：

- 窗口 B 发现问题 → 记录到 Changelog 或 Troubleshooting → 把优化建议传给窗口 A
- 窗口 A 接到修改意见 → 执行优化 → 提交新版本

这样做就像让两个 AI 工程师分工合作，一个埋头干活，一个吹毛求疵，互相制衡。

举个🌰：

一开始我在做 **LeetCode 动画可视化** 的小工具：

1.  **窗口 A** 用 Python + Pygame 画图，先实现了一个能跑通的原型。
2.  **窗口 B** 盯着代码看，立刻指出：

- 动画帧率不稳定（需要加 delta time 控制）
- 内存占用偏高（贴图缓存没释放）
- 某段循环逻辑过于冗余（可以用生成器优化）

于是，窗口 B 把这些问题写进 Troubleshooting 文档，还在 Changelog 标记了“ [v0.1](http://v0.1) → [v0.2](http://v0.2) 优化项”。

窗口 A 根据这些意见重构了一次，性能直接提升 30%。

如果只有一个窗口，我很可能写到最后，才发现动画卡得像 PPT，还得翻半天聊天记录找之前的错误描述，心态崩掉。

## 为什么这种工作流效率高

1.  **上下文干净**Claude Code 在窗口 A 的记忆不会被 debug 信息污染，生成的代码质量更稳定。
2.  **反馈循环清晰**问题被明确记录，避免“上次 Claude 说啥来着”的尴尬。
3.  **可追溯**Changelog 和 Troubleshooting 像是团队的知识库，下一次遇到类似问题，可以秒查秒改。
4.  **更接近真实协作**人类团队里，开发和测试是分开的。AI 团队也是一样，分工才能稳定产出。

## 总结

Claude Code 本质上就是一个可以无休无眠工作的“虚拟工程师”，你完全可以用**多窗口 + 分工**的方式，让它变成一支小型 AI 团队：

- **一个窗口写**
- **一个窗口挑刺**
- **文档沉淀经验**


---

<!-- 文件: Claude_Code_=_程序员的情绪日记.md | 创建时间: 2025-08-25 16:38:52 -->

你想知道过去一段时间，自己哪天情绪好，哪天情绪不好吗？

如果你和我一样日常使用Claude Code比较多，那就可以使用ccusage查看自己每一天的记录。

到底哪一天在摸鱼，哪一天在突飞猛进，一目了然。

![图片](https://mmbiz.qpic.cn/mmbiz_png/607DKnuWzlHVcaRIxVuAI1Of4OwficQBTxpYBia814wuQn78wI8EI9uzjG02dnN7c3ia1yj4eibUJbiarILxklDKaYw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)

在命令行输入

```nginx
bunx ccusage
```

即可查看。

如果你喜欢用npx，也可以输入

```nginx
npx ccusage@latest
```

同时也感慨，Anthropic 以$200美元/月的价格卖包月套餐给我，它恐怕真是亏大了，从7月1日以来，已经干了它价值$4338.80美元的Token了。

你的呢？请在评论区贴出你的心情日记吧～～


---

<!-- 文件: Claude_Code_遭深度逆向！核心技术架构被_95_还原.md | 创建时间: 2025-08-25 16:39:05 -->

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qH0XkVXvKZTjHu2KEOcu6o260lH2JX5Y1Bpdnh9gFsmkE1UkDgrPKzKLNfChL4ldWQIrsYst2d0sg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)

前几天晚上，我在 GitHub 上看到一个让我眼睛发直的项目。

一个叫 **shareAI‑lab** 的团队对 Claude Code 进行了彻底逆向，并把完整的研究资料、中间的分析过程全部 po 了出来。

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0fK12dehO0H17V8087fOUVHz2NicbBTibd6iaa2xRzWCfW4vpZhoIdzicyw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

Claude Code 可是 Anthropic 家的当红炸子鸡，是他们在 AI coding 这条路上最拿得出手的产品。

但现在，Claude Code 的底裤被一个民间逆向仓库扒了，曝光了核心技术架构、实现机制和运行逻辑，相当于做了个开箱拆机，连怎么听懂人话、怎么调用工具、怎么记住上下文、怎么防恶意指令，全都曝光了。

仓库地址我放在这里了：

> [https://github.com/shareAI-lab/analysis_claude_code](https://github.com/shareAI-lab/analysis_claude_code)

（PS：这个项目目前在 archive，作者佬在小红书回应还在更新中）

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0oslBql4nMv0TH4BrX5XQhjbCLqQ1NH6vSbwlWoDmKaCtAFLw13Eg2w/640?wx_fmt=jpeg&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

先铺个背景方便大家伙儿理解——

大家都知道 Claude Code 本身是闭源的，但为了让 CLI 正常跑，他们还是得把代码随安装包发给用户。所以 CLI 里还打包了一份 **50 k+ 行的混淆 JavaScript 代码，**只是这份代码被 刻意“打乱、加密、改名”，目的就是把核心算法和 Prompt 逻辑藏起来，让人看不懂，避免别人抄袭了去。这就叫 JavaScript 混淆。

但是 JS 终究要跑在本地，再怎么混淆， [Node.js](http://node.js/) 终究要看到可执行的明文逻辑，这就给逆向者提供了入口。

那这位民间逆向者是咋做的呢？

他们是用 claude code 去分析 claude code（ [v1.0.33）本身的混淆后代码\*\*](<http://v1.0.xn--33)%2A%2A-s27ny86b619cz6ruta893hk8e7r6i/>) \*\*，（哎？听起来像套娃）

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0bIxIo0bvOFSJQic66udm66EXibM7kpB2XBIfe3tmhGj23wVCydYfbfOA/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

也就是对 5 万行的混淆代码切片，借助 Claude Code 的力量分析 15 个 chunks 文件，再用人肉 + 调试补洞，最后拼出来一份 95% 准确度的“推断版架构”。

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0SF6nCBxUpkbmlCEdf3mLibqVxTSLfGvfibTXEYw7NjHyiaClbIm9icFtBA/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

**【友情提示】**：下面的逆向笔记并非官方文档，README 里写得很直白——“非 100 % 准确，分析过程中 LLM 难免出现幻觉，仅供学习参考”。

先来看看这份逆向推断版的 Claude Code 系统架构全景图：

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0G7k8qqBSZNbjZZsbnUztvYaNgdHu5v7R7iaTFibsCM2oiaibppBCUzwEUA/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

最核心的技术映射如下——

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0WQVib4YTaPfUPyDb2jibFJDTqECT2fFaWAoOk25PYiajEq9dMGKySgHMw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

最顶层是用户交互层。

无论你是在命令行里敲 Claude、在 VSCode 用插件，还是在 Web 页面上跑，它们背后对接的其实是同一套调度系统。

这一层只负责接收你的指令，并把它们统一编码为 Claude Agent 系统能理解的请求格式。也就是说，不管你从哪个入口发出指令，最终都会被转化为统一的数据格式，由 “Claude 模型大脑”接收和处理。

而这个“大脑”在中间层——**Agent 核心调度层**。

中心是一个叫 nO 的主循环引擎（其实就是 AgentLoop），它负责管理一切智能体行为的“总调度室”。流程图是这样的：

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb09SHhS91A9iczsUulKvp3tUOFtnt7ic6qPbdj4uj08Zc20s7ibrIquxv5Q/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

你每输一句话，它就得判断：

- 是不是新任务？
- 需要调用哪些工具？
- 哪些 Agent 该被唤醒？
- 哪些历史信息要压缩？
- 有没有地方出错要补救？

这些决策的执行，要靠它左手的h2A 消息队列**（负责异步传输和流式反馈），右手的** wu 会话流生成器**（实时生成文字输出），加上一套名为** wU2 的压缩引擎来动态优化你用过的上下文。

注意，这里没有一个地方是模型在跑。模型本身只是调度结果中的一个工具，它只是整个流程中的一个“被调用者”。真正做判断、做协调的，是这一整套调度引擎和运行时逻辑。

往下是**工具执行与管理层**，也是 Claude Code 最像“中台”的地方。

它负责调度具体的子 Agent。比如你发一个“运行 shell 命令”的请求，它就会调出负责 bash 执行的 Agent；你要求读取项目目录，它就找出读写权限最小的文件管理 Agent。

这些 Agent 都受控于几大核心部件：

- **MH1 工具引擎**：发现工具、校验参数、分配任务；
- **UH1 并发调度器**：限制并发量、防止资源争抢；
- **SubAgent 管理器**：给每个子任务分配独立 Agent，并做任务隔离；
- **权限验证网关**：判断你这个 Agent 能不能运行某条命令、能不能访问某个文件、有没有联网权限。

也就是说，Claude 不是一次性调一个“大助手”来干活，而是每个任务都生成一个独立的“子 Agent”，然后严格按照权限、状态、工具能力来分发执行。

继续往下，是**工具生态系统**。

这就是 Claude Code 真正的“武器库”。上百个分类明确、职责清晰的小工具，从文件读写、命令执行，到网络搜索、任务管理、MCP 集成、性能诊断应有尽有。

![图片](https://mmbiz.qpic.cn/mmbiz_png/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb0FfJ2xdj8jH3135bpZgOiaPQvMOkicgYlg22xYicKsK3Fbo0jUqoWTyslQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

你以为 Claude 在思考，其实它只是在调用：

- 谁擅长这类问题？哪个 Agent 适合？
- 有没有需要配合的两个工具一起跑？

这种工具生态不是插件，而是结构化地配置在系统里。

工具的定义方式是文件级别，每一个工具都是一个可管理、可审计、可热加载的模块单元。你甚至可以自己写一个 .yaml 文件扔进目录里，Claude 立马能发现它、加载它、赋权限。

最底层，是**存储与持久化系统**。

这是 Claude 记忆力的来源，整个记忆架构分三层。

![图片](https://mmbiz.qpic.cn/mmbiz_jpg/5fknb41ib9qFp3QEhjibpcibUYc52BqTvb00iaS4uaRjIx5iaLibGibh2QfJN7LFxzulPRNwCqysc161vKft84gmboS6Q/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

它是按时间维度、压缩策略、任务粒度分层处理记忆：

- 当前会话 → 放在 Messages 里，支持即时交互；
- 中期摘要 → 放进 Compressed 模块，由 wU2 压缩器负责优化；
- 永久偏好 → 写入 [CLAUDE.md，包括你常用语言、项目结构、喜好工具等；](http://claude.xn--md,;-nw3ca9237cmrdi7dsvid0l0jny4at59bdiqkp5ctkgv7uw0p1w6c1pib55e/)
- 系统状态 → 存在 StateCache 里，比如某工具运行次数、是否曾报错、是否因权限受限被禁用等。

每一次调用、每一个决策，其实都依赖于这些存储结构的回忆。

Claude Code 并不依赖于云端记忆，而是靠本地状态文件、上下文压缩算法、状态缓存系统构建出一个“类人记忆”的思维体系。

这就是 Claude Code 系统架构的全貌。

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

它把一套多 Agent 系统跑得像流水线一样顺滑。Claude Code 早就不是一个“智能补全”的工具了，它是一套 AI 时代的“本地分布式 Agent 操作系统”。

说到这里，很多人可能还是觉得，这不就是多加了几个 Agent 和工具嘛，有啥真正厉害的地方？

错了。

如果你真的打开那份逆向分析文档，你会看到一个句子像电流一样穿过代码注释和调度日志：**Claude Code 的真正突破，不在于调了几个工具，而在于它让这些 Agent 之间的协作，变成了“实时的、稳态的、动态可控”的过程。**

简单说，它不仅能调，还能边调边改方向，边跑边让不同 Agent 对齐节奏。这听起来像废话，但工程上能做到的几乎没有。

另外，项目作者还整理了这里面的重要的技术创新，实时 Steering 技术和 智能上下文压缩算法。

### **实时 Steering：从“触发”到“引导”的跃迁**

大多数 AI 工具的调度逻辑是触发式的，也就是你下个请求，我执行一次；你换个指令，我再跑一遍。但 Claude Code 的 h2A 消息队列，不是“等你发完才处理”，而是能在指令刚输入一半时就启动流程，并边接收、边调度、边调整。

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

我们在逆向文档里看到它的核心机制用的是“双缓冲队列 + 条件触发消费”，伪代码如下：

```javascript
class h2AAsyncMessageQueue {
  enqueue(message) {
    // 策略1: 零延迟路径 - 直接传递给等待的读取者
    if (this.readResolve) {
      this.readResolve({ done: false, value: message });
      this.readResolve = null;
      return;
    } // 策略2: 缓冲路径 - 存储到循环缓冲区

    this.primaryBuffer.push(message);
    this.processBackpressure();
  }
}
```

简单来说，它不是等消息“堆满”才动，而是**只要有人等，它就立刻传；没人等，它就缓冲 + 限流**。再加上流式写回机制，这就保证了 Claude 可以边生成文字、边调整任务、边响应新输入。

这才是真正的“Steering”，你能在它做的时候，随时发指令“换方向”，它立刻响应。

### **智能上下文压缩：用算法判断保留谁在说话**

Claude 的第二个重大创新，是我们看到的 wU2 上下文压缩系统。

很多 AI 产品都在解决一个问题：上下文太长，token 爆炸，要裁剪。但大多数产品是靠“历史越久越删”“内容越长越删”，要么全砍，要么硬塞。

Claude 不一样。它用了一种 “重要性加权 + 策略性摘要”的压缩法。

比如这段触发逻辑：

```javascript
// 压缩触发逻辑
if (tokenUsage > CONTEXT_THRESHOLD * 0.92) {
  const compressedContext = await wU2Compressor.compress({
    messages: currentContext,
    preserveRatio: 0.3,
    importanceScoring: true,
  });
}
```

意思是，当 token 使用量超过阈值 92%，系统就会调用压缩器进行上下文重构。但不是压缩全部，而是按“重要性”打分，**只保留 30% 的最关键段落**，剩下的提炼成摘要。

这一设计让 Claude 在执行任务时，可以更精准地维持上下文的“记忆完整度”。压缩操作不以时间或长度为主维度，而是以内容关键性为准则，减少冗余信息对模型推理的干扰，同时维持对历史任务、用户偏好和中间变量的追踪能力。

这也是为什么用户在与 Claude 进行长时间交互时，会感觉它记得住，并且记得的都是重点，不容易断片。

从这次的逆向文档中，我们第一次清晰地看到了什么是真正有工程厚度的 Agent 产品。

它并不追求一句话能做多少事，而是让每一句话的背后，都能安全、高效、合理地调度十个 Agent。

而且关键是，**它是真的跑起来了**。

它让我们看到一个事实：

> 未来的 AI 编程助手，不会是 ChatGPT 的一个功能分支，而是一个具备工程稳定性、安全性、组织能力的智能体操作平台。

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)


---

<!-- 文件: Claude_code_要限制了，即使你是max用户.md | 创建时间: 2025-08-25 16:39:41 -->

1\. 什么事？ Claude Pro 和 Max 要设置每周用量上限了

2\. 什么时候？ 8 月 28 日开始

3\. 为什么？

\* 成本太高：有极少数用户（公告里称为“超级粉丝”）24小时不停地用，一个200美元套餐的用户能跑出几万美元的成本，公司扛不住了  
\* 有人违规：还有少数人共享、转卖账号，占用了资源，影响了大家

4\. 对我影响大吗？ 可能不大。官方估计，只有不到 5% 的用户会受到影响

5\. 用量超了怎么办？ 如果你是 Max 用户，用超了可以按 API 的标准价格加钱买

6\. 官方态度：官方表示这是为了保证服务稳定，同时欢迎“重度用户”（Power User）给他们提建议，帮助他们找到更好的服务方式。

ClaudeCode 安装教程： [https://yoo7f1r4m4.feishu.cn/wiki/WEhlwyO83iZC4WknX8CcscnEnPh](https://yoo7f1r4m4.feishu.cn/wiki/WEhlwyO83iZC4WknX8CcscnEnPh)
