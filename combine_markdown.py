"""
Markdown文件合并工具

这个模块提供了将多个Markdown文件合并为单个文件的功能。
文件会根据文件名中的章节号进行排序，支持多种命名格式。
"""
import os
import re

def combine_markdown_files(directory=".", output_file="combined.md"):
    """
    Combines multiple markdown files in a directory into a single markdown file,
    ordering them based on the chapter number found in their filenames.
    """

    markdown_files = [f for f in os.listdir(directory) if f.endswith(".md")]

    def get_chapter_number(filename):
        """
        Extracts the chapter number from the filename.
        Handles formats like "1.md", "0 前言.md", "第7章.md".
        Returns the number or float('inf') if no number is found.
        """
        # Try matching patterns like "第7章.md" or "1.md" or "0 前言.md"
        # Use findall to get all numbers, take the first one if found.
        numbers = re.findall(r"\d+", filename)
        if numbers:
            return int(numbers[0])
        return float('inf')  # Files with no chapter number will be last

    # Sort files based on chapter number using sorted() function
    markdown_files = sorted(markdown_files, key=get_chapter_number)

    with open(output_file, "w", encoding="utf-8") as outfile:
        for filename in markdown_files:
            filepath = os.path.join(directory, filename)
            with open(filepath, "r", encoding="utf-8") as infile:
                outfile.write(infile.read())
                outfile.write("\\n\\n")  # Add a separator between files

    print(f"Combined markdown files into {output_file}")

if __name__ == "__main__":
    combine_markdown_files(directory="files")

