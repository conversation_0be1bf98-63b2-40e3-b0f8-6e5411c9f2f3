"""
Markdown文件合并器GUI应用程序

这个GUI应用程序提供了一个用户友好的界面来合并多个Markdown文件。
支持拖拽操作、文件选择、进度显示和错误处理。
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinter.scrolledtext as scrolledtext
import os
import threading
import tempfile
import shutil
from pathlib import Path
import sys

# 尝试导入拖拽支持库
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    DRAG_DROP_AVAILABLE = False

# 导入现有的合并模块
try:
    from combine_markdown_by_time import combine_markdown_files_by_time, combine_markdown_file_list
except ImportError:
    # 如果直接运行此文件，尝试从当前目录导入
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from combine_markdown_by_time import combine_markdown_files_by_time, combine_markdown_file_list


class MarkdownCombinerGUI:
    """Markdown文件合并器GUI主类"""
    
    def __init__(self, root):
        """初始化GUI界面"""
        self.root = root
        self.root.title("Markdown文件合并器 - 按创建时间排序")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 文件列表
        self.selected_files = []
        self.output_file_path = ""
        
        # 创建界面
        self.create_widgets()
        
        # 绑定拖拽事件
        self.setup_drag_drop()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Markdown文件合并器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择Markdown文件", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # 拖拽提示
        drag_label = ttk.Label(file_frame, 
                              text="拖拽.md文件到此处，或点击下方按钮选择文件",
                              font=("Arial", 10))
        drag_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 文件列表（Treeview 两列：文件名、路径）
        self.file_tree = ttk.Treeview(
            file_frame,
            columns=("name", "path"),
            show="headings",
            selectmode="extended",
            height=8,
        )
        self.file_tree.heading("name", text="文件名")
        self.file_tree.heading("path", text="路径")
        # 初始列宽，并允许拉伸；路径列更宽
        self.file_tree.column("name", width=200, stretch=True, anchor=tk.W)
        self.file_tree.column("path", width=500, stretch=True, anchor=tk.W)
        self.file_tree.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 滚动条
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.file_tree.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        # 添加文件按钮
        self.add_files_btn = ttk.Button(button_frame, text="添加文件", command=self.add_files)
        self.add_files_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 移除文件按钮
        self.remove_files_btn = ttk.Button(button_frame, text="移除选中", command=self.remove_selected_files)
        self.remove_files_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空列表按钮
        self.clear_files_btn = ttk.Button(button_frame, text="清空列表", command=self.clear_files)
        self.clear_files_btn.pack(side=tk.LEFT)
        
        # 输出设置区域
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)
        
        # 输出文件路径
        ttk.Label(output_frame, text="输出文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_path_var = tk.StringVar(value="combined_by_time.md")
        self.output_path_entry = ttk.Entry(output_frame, textvariable=self.output_path_var)
        self.output_path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 浏览按钮
        self.browse_btn = ttk.Button(output_frame, text="浏览", command=self.browse_output_file)
        self.browse_btn.grid(row=0, column=2)
        
        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        # 开始合并按钮
        self.combine_btn = ttk.Button(action_frame, text="开始合并", 
                                     command=self.start_combine, style="Accent.TButton")
        self.combine_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(action_frame, variable=self.progress_var, 
                                          mode='indeterminate', length=200)
        self.progress_bar.pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(action_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(4, weight=1)
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        if DRAG_DROP_AVAILABLE and hasattr(self.file_tree, 'drop_target_register'):
            # 使用tkinterdnd2库实现真正的拖拽功能
            self.file_tree.drop_target_register(DND_FILES)
            if hasattr(self.file_tree, 'dnd_bind'):
                self.file_tree.dnd_bind('<<Drop>>', self.on_drop)
            self.log_message("拖拽功能已启用 - 可以直接拖拽文件到列表中")
        else:
            # 如果没有拖拽库，提供替代方案
            self.file_tree.bind('<Double-Button-1>', lambda _: self.add_files())
            self.log_message("拖拽功能不可用 - 请使用'添加文件'按钮或双击列表选择文件")

    def on_drop(self, event):
        """处理文件拖拽事件"""
        if not DRAG_DROP_AVAILABLE:
            return

        # 获取拖拽的文件路径
        files = self.root.tk.splitlist(event.data)
        md_files = []

        for file_path in files:
            # 检查是否为.md文件
            if file_path.lower().endswith('.md') and os.path.isfile(file_path):
                if file_path not in self.selected_files:
                    md_files.append(file_path)
                    self.selected_files.append(file_path)
                    filename = os.path.basename(file_path)
                    self.file_listbox.insert(tk.END, f"{filename} ({file_path})")

        if md_files:
            self.log_message(f"通过拖拽添加了 {len(md_files)} 个Markdown文件")
            self.update_ui_state()
        else:
            self.log_message("拖拽的文件中没有有效的Markdown文件(.md)")

        return event.action
        
    def add_files(self):
        """添加文件到列表"""
        files = filedialog.askopenfilenames(
            title="选择Markdown文件",
            filetypes=[("Markdown files", "*.md"), ("All files", "*.*")]
        )
        
        added = 0
        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                filename = os.path.basename(file_path)
                self.file_tree.insert("", tk.END, values=(filename, file_path))
                added += 1

        if added:
            self.log_message(f"添加了 {added} 个文件")
        self.update_ui_state()

    def remove_selected_files(self):
        """移除选中的文件"""
        selected = self.file_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要移除的文件")
            return

        # 删除 Treeview 中选中项，并同步 selected_files
        removed = 0
        # 获取所有项的当前顺序，构建 path -> item_id 映射
        # 但我们直接根据条目值找到路径并删除即可
        for item_id in selected:
            values = self.file_tree.item(item_id, 'values')
            if len(values) >= 2:
                path = values[1]
                if path in self.selected_files:
                    self.selected_files.remove(path)
            self.file_tree.delete(item_id)
            removed += 1

        if removed:
            self.log_message(f"移除了 {removed} 个文件")
        self.update_ui_state()

    def clear_files(self):
        """清空文件列表"""
        if self.selected_files:
            # 清空 Treeview 与内部列表
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)
            self.selected_files.clear()
            self.log_message("已清空文件列表")
            self.update_ui_state()

    def browse_output_file(self):
        """浏览输出文件位置"""
        file_path = filedialog.asksaveasfilename(
            title="选择输出文件位置",
            defaultextension=".md",
            filetypes=[("Markdown files", "*.md"), ("All files", "*.*")]
        )
        
        if file_path:
            self.output_path_var.set(file_path)
            
    def update_ui_state(self):
        """更新UI状态"""
        has_files = len(self.selected_files) > 0
        self.combine_btn.configure(state=tk.NORMAL if has_files else tk.DISABLED)
        self.remove_files_btn.configure(state=tk.NORMAL if has_files else tk.DISABLED)
        self.clear_files_btn.configure(state=tk.NORMAL if has_files else tk.DISABLED)
        
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.configure(state=tk.DISABLED)
        self.log_text.see(tk.END)
        
    def start_combine(self):
        """开始合并文件"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要合并的文件")
            return

        output_path = self.output_path_var.get().strip()
        if not output_path:
            messagebox.showwarning("警告", "请指定输出文件路径")
            return

        # 后台线程执行合并操作
        self.combine_btn.configure(state=tk.DISABLED)
        self.progress_bar.config(mode='determinate')
        self.progress_var.set(0)
        self.progress_bar['maximum'] = max(1, len(self.selected_files))
        self.status_var.set("正在合并...")
        self.root.after(0, self.log_message, f"开始合并 {len(self.selected_files)} 个文件...")

        thread = threading.Thread(target=self.combine_files_thread, args=(output_path,))
        thread.daemon = True
        thread.start()

    def combine_files_thread(self, output_path):
        """在后台线程中执行文件合并（线程安全：仅通过after更新UI）"""
        try:
            # 定义进度回调
            def on_progress(done, total, current_path):
                self.root.after(0, self.progress_var.set, done)
                name = os.path.basename(current_path)
                self.root.after(0, self.log_message, f"处理: {name} ({done}/{total})")

            # 使用新API，直接按用户选择的路径列表合并，避免重名冲突
            combine_markdown_file_list(
                self.selected_files,
                output_path,
                progress_callback=on_progress,
            )
            self.root.after(0, self.combine_success, output_path)
        except Exception as e:
            self.root.after(0, self.combine_error, f"合并过程中发生错误: {str(e)}")
            
    def combine_success(self, output_path):
        """合并成功的处理"""
        self.progress_bar.stop()
        self.combine_btn.configure(state=tk.NORMAL)
        self.status_var.set("合并完成")
        
        self.log_message(f"合并完成！输出文件: {output_path}")
        
        # 询问是否打开输出文件
        result = messagebox.askyesno("合并完成", 
                                   f"文件合并完成！\n输出文件: {output_path}\n\n是否打开输出文件？")
        if result:
            self.open_output_file(output_path)
            
    def combine_error(self, error_message):
        """合并失败的处理"""
        self.progress_bar.stop()
        self.combine_btn.configure(state=tk.NORMAL)
        self.status_var.set("合并失败")
        
        self.log_message(f"错误: {error_message}")
        messagebox.showerror("合并失败", error_message)
        
    def open_output_file(self, file_path):
        """打开输出文件"""
        try:
            import subprocess
            import platform

            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", file_path], check=False)
            elif system == "Windows":
                subprocess.run(["start", file_path], shell=True, check=False)
            else:  # Linux
                subprocess.run(["xdg-open", file_path], check=False)
        except OSError as e:
            self.log_message(f"无法打开文件: {str(e)}")


def main():
    """主函数"""
    # 如果有拖拽支持，使用TkinterDnD
    if DRAG_DROP_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()

    MarkdownCombinerGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        # 可以在这里设置应用程序图标
        pass
    except Exception:
        pass

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
