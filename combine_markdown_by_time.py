"""
按时间排序的Markdown文件合并工具

这个模块提供了将多个Markdown文件合并为单个文件的功能。
文件会根据创建时间进行排序，创建时间较早的文件内容排在前面。
"""
import os
import time

from typing import Callable, Iterable, List, Tuple


def _get_creation_time(filepath: str) -> float:
    """获取文件的创建时间（跨平台尽量一致）。

    优先使用 macOS 的 st_birthtime；否则使用 ctime 与 mtime 的较小值。
    获取失败时返回 float('inf')，使该文件排在最后。
    """
    try:
        stat_info = os.stat(filepath)
        if hasattr(stat_info, 'st_birthtime'):
            return stat_info.st_birthtime
        return min(stat_info.st_ctime, stat_info.st_mtime)
    except OSError as e:
        print(f"警告：无法获取文件 '{os.path.basename(filepath)}' 的创建时间: {e}")
        return float('inf')


essential_sep = "\n\n---\n\n"


def combine_markdown_file_list(
    file_paths: Iterable[str],
    output_file: str,
    progress_callback: Callable[[int, int, str], None] | None = None,
    verbose: bool = True,
) -> None:
    """
    将给定路径列表的多个Markdown文件按创建时间排序合并为一个文件。

    Args:
        file_paths: 需要合并的Markdown文件绝对路径或相对路径列表
        output_file: 输出文件路径
        progress_callback: 进度回调，签名为 (done_count, total_count, current_path)
        verbose: 是否打印合并结果与顺序信息
    """
    file_paths = [p for p in file_paths if p and str(p).lower().endswith('.md')]
    if not file_paths:
        print("在提供的文件列表中没有找到Markdown文件")
        return

    # 计算创建时间并做稳定排序：时间 -> 文件名 -> 绝对路径
    files_with_time: List[Tuple[str, float]] = [
        (p, _get_creation_time(p)) for p in file_paths
    ]
    files_with_time.sort(
        key=lambda x: (
            x[1],
            os.path.basename(x[0]).lower(),
            os.path.abspath(x[0]).lower(),
        )
    )

    try:
        with open(output_file, "w", encoding="utf-8") as outfile:
            total = len(files_with_time)
            for i, (path, creation_time) in enumerate(files_with_time):
                try:
                    with open(path, "r", encoding="utf-8") as infile:
                        content = infile.read()
                except UnicodeDecodeError as e:
                    print(
                        f"警告：文件 '{os.path.basename(path)}' 编码错误，跳过该文件: {e}"
                    )
                    if progress_callback:
                        progress_callback(i + 1, total, path)
                    continue
                except OSError as e:
                    print(f"警告：无法读取文件 '{os.path.basename(path)}': {e}")
                    if progress_callback:
                        progress_callback(i + 1, total, path)
                    continue

                # 写入注释与内容
                if creation_time != float('inf'):
                    time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(creation_time))
                else:
                    time_str = "未知"
                outfile.write(
                    f"<!-- 文件: {os.path.basename(path)} | 创建时间: {time_str} -->\n\n"
                )
                outfile.write(content)
                if i < total - 1:
                    outfile.write(essential_sep)

                if progress_callback:
                    progress_callback(i + 1, total, path)

    except OSError as e:
        print(f"错误：无法创建输出文件 '{output_file}': {e}")
        return

    if verbose:
        print(f"成功将 {len(files_with_time)} 个Markdown文件按创建时间顺序合并到 '{output_file}'")
        print("\n文件处理顺序（按创建时间排序）：")
        for idx, (path, creation_time) in enumerate(files_with_time, 1):
            if creation_time != float('inf'):
                time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(creation_time))
            else:
                time_str = "未知"
            print(f"  {idx}. {os.path.basename(path)} ({time_str})")

def combine_markdown_files_by_time(directory=".", output_file="combined_by_time.md"):
    """
    Combines multiple markdown files in a directory into a single markdown file,
    ordering them based on their creation time (earliest first).

    Args:
        directory (str): 要扫描的目录路径，默认为当前目录
        output_file (str): 输出文件名，默认为"combined_by_time.md"

    Returns:
        None

    Raises:
        OSError: 当目录不存在或文件访问权限不足时
        UnicodeDecodeError: 当文件编码不是UTF-8时
    """

    # 检查目录是否存在
    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在")
        return

    # 获取目录中所有的markdown文件
    try:
        markdown_files = [f for f in os.listdir(directory) if f.endswith(".md")]
    except OSError as e:
        print(f"错误：无法读取目录 '{directory}': {e}")
        return

    if not markdown_files:
        print(f"在目录 '{directory}' 中没有找到Markdown文件")
        return

    def get_creation_time(filename):
        """
        获取文件的创建时间。

        Args:
            filename (str): 文件名

        Returns:
            float: 文件创建时间的时间戳，如果获取失败返回当前时间
        """
        try:
            filepath = os.path.join(directory, filename)
            # 在不同操作系统上获取创建时间的方法可能不同
            # 使用 st_ctime (在Unix系统上是状态改变时间，在Windows上是创建时间)
            # 或者使用 st_mtime (修改时间) 作为备选
            stat_info = os.stat(filepath)

            # 优先使用创建时间，如果不可用则使用修改时间
            if hasattr(stat_info, 'st_birthtime'):
                # macOS 系统支持真正的创建时间
                return stat_info.st_birthtime
            # 其他系统使用 ctime 或 mtime
            return min(stat_info.st_ctime, stat_info.st_mtime)
        except OSError as e:
            print(f"警告：无法获取文件 '{filename}' 的创建时间: {e}")
            return float('inf')  # 返回无穷大，使有问题的文件排在最后

    # 获取文件创建时间并排序
    try:
        # 创建文件名和创建时间的元组列表
        files_with_time = [(filename, get_creation_time(filename)) for filename in markdown_files]
        # 按创建时间排序（稳定排序）：时间 -> 文件名
        files_with_time.sort(key=lambda x: (x[1], str(x[0]).lower()))
    except OSError as e:
        print(f"错误：排序文件时出现问题: {e}")
        return

    # 合并文件
    try:
        with open(output_file, "w", encoding="utf-8") as outfile:
            for i, (filename, creation_time) in enumerate(files_with_time):
                filepath = os.path.join(directory, filename)

                try:
                    with open(filepath, "r", encoding="utf-8") as infile:
                        content = infile.read()

                        # 添加文件信息注释
                        if creation_time != float('inf'):
                            time_str = time.strftime("%Y-%m-%d %H:%M:%S",
                                                    time.localtime(creation_time))
                        else:
                            time_str = "未知"
                        outfile.write(f"<!-- 文件: {filename} | 创建时间: {time_str} -->\n\n")

                        # 写入文件内容
                        outfile.write(content)

                        # 在文件之间添加分隔符（除了最后一个文件）
                        if i < len(files_with_time) - 1:
                            outfile.write("\n\n---\n\n")
                except UnicodeDecodeError as e:
                    print(f"警告：文件 '{filename}' 编码错误，跳过该文件: {e}")
                    continue
                except OSError as e:
                    print(f"警告：无法读取文件 '{filename}': {e}")
                    continue

    except OSError as e:
        print(f"错误：无法创建输出文件 '{output_file}': {e}")
        return

    print(f"成功将 {len(files_with_time)} 个Markdown文件按创建时间顺序合并到 '{output_file}'")

    # 显示文件处理顺序
    print("\n文件处理顺序（按创建时间排序）：")
    for i, (filename, creation_time) in enumerate(files_with_time, 1):
        if creation_time != float('inf'):
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(creation_time))
        else:
            time_str = "未知"
        print(f"  {i}. {filename} ({time_str})")

if __name__ == "__main__":
    # 默认处理当前目录下的文件，输出到 combined_by_time.md
    combine_markdown_files_by_time(directory=".", output_file="combined_by_time.md")
