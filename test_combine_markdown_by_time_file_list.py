"""
测试 combine_markdown_by_time.combine_markdown_file_list 的单元测试
"""
import os
import tempfile
import shutil
import time
import unittest

from combine_markdown_by_time import combine_markdown_file_list


class TestCombineMarkdownFileList(unittest.TestCase):
    def setUp(self):
        self.tmpdir = tempfile.mkdtemp()
        self.outfile = os.path.join(self.tmpdir, "out.md")

    def tearDown(self):
        shutil.rmtree(self.tmpdir, ignore_errors=True)

    def _make_file(self, name: str, content: str, delay: float = 0.1) -> str:
        path = os.path.join(self.tmpdir, name)
        with open(path, "w", encoding="utf-8") as f:
            f.write(content)
        if delay:
            time.sleep(delay)
        return path

    def test_basic_ordering_and_output(self):
        a = self._make_file("a.md", "# A")
        b = self._make_file("b.md", "# B")
        combine_markdown_file_list([b, a], self.outfile)
        with open(self.outfile, "r", encoding="utf-8") as f:
            data = f.read()
        self.assertLess(data.find("# A"), data.find("# B"))
        self.assertIn("<!-- 文件: a.md", data)
        self.assertIn("<!-- 文件: b.md", data)

    def test_duplicate_filenames_from_different_dirs(self):
        # 创建另一临时目录，内含同名文件 a.md
        other = tempfile.mkdtemp()
        try:
            p1 = self._make_file("a.md", "# One")
            p2 = os.path.join(other, "a.md")
            with open(p2, "w", encoding="utf-8") as f:
                f.write("# Two")
            # 两个文件名相同，不应相互覆盖，且都应出现在输出中
            combine_markdown_file_list([p1, p2], self.outfile)
            with open(self.outfile, "r", encoding="utf-8") as f:
                data = f.read()
            self.assertIn("# One", data)
            self.assertIn("# Two", data)
        finally:
            shutil.rmtree(other, ignore_errors=True)

    def test_stable_sort_with_equal_timestamps(self):
        # 强制相同时间戳：创建快后 touch 修改时间一致
        p1 = self._make_file("x.md", "# X", delay=0)
        p2 = self._make_file("y.md", "# Y", delay=0)
        # 将mtime/ctime尽量设置为相同（某些系统可能不可完全控制，但排序还有文件名次键）
        st = os.stat(p1)
        os.utime(p2, (st.st_atime, st.st_mtime))
        # 使用文件名作为次键，x.md 应排在 y.md 前
        combine_markdown_file_list([p2, p1], self.outfile)
        with open(self.outfile, "r", encoding="utf-8") as f:
            data = f.read()
        self.assertLess(data.find("# X"), data.find("# Y"))


if __name__ == "__main__":
    unittest.main(verbosity=2)

