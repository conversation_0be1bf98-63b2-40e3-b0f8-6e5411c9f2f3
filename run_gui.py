#!/usr/bin/env python3
"""
Markdown文件合并器GUI启动脚本

这个脚本提供了一个简单的方式来启动GUI应用程序，
包括依赖检查和错误处理。
"""
import sys
import os
import subprocess


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("❌ 错误：需要Python 3.6或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    return True


def check_tkinter():
    """检查tkinter是否可用"""
    try:
        import tkinter
        return True
    except ImportError:
        print("❌ 错误：tkinter不可用")
        print("请安装tkinter：")
        print("  Ubuntu/Debian: sudo apt-get install python3-tk")
        print("  CentOS/RHEL: sudo yum install tkinter")
        print("  macOS: tkinter通常随Python一起安装")
        return False


def check_core_module():
    """检查核心合并模块"""
    try:
        from combine_markdown_by_time import combine_markdown_files_by_time
        return True
    except ImportError:
        print("❌ 错误：找不到核心模块 combine_markdown_by_time.py")
        print("请确保该文件在当前目录中")
        return False


def check_drag_drop():
    """检查拖拽功能支持"""
    try:
        import tkinterdnd2
        print("✅ 拖拽功能可用")
        return True
    except ImportError:
        print("⚠️  拖拽功能不可用（可选）")
        print("要启用拖拽功能，请运行：python install_dependencies.py")
        return False


def main():
    """主函数"""
    print("Markdown文件合并器GUI - 启动检查")
    print("=" * 50)
    
    # 检查系统要求
    checks = [
        ("Python版本", check_python_version),
        ("tkinter库", check_tkinter),
        ("核心模块", check_core_module),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"检查{name}...", end=" ")
        if check_func():
            print("✅ 通过")
        else:
            print("❌ 失败")
            all_passed = False
    
    # 检查可选功能
    print("\n检查可选功能:")
    check_drag_drop()
    
    print("\n" + "=" * 50)
    
    if not all_passed:
        print("❌ 系统检查失败，无法启动GUI")
        print("请解决上述问题后重试")
        return False
    
    print("✅ 系统检查通过，正在启动GUI...")
    
    try:
        # 启动GUI应用程序
        from markdown_combiner_gui import main as gui_main
        gui_main()
        return True
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return True
    except Exception as e:
        print(f"❌ 启动GUI时发生错误：{e}")
        print("\n故障排除建议：")
        print("1. 确保所有必需文件都在当前目录")
        print("2. 检查Python环境是否正确配置")
        print("3. 尝试直接运行：python markdown_combiner_gui.py")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按Enter键退出...")
        sys.exit(1)
