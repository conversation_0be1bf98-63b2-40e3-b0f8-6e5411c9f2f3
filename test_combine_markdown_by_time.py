"""
测试 combine_markdown_by_time.py 模块的单元测试

这个测试文件包含了对 combine_markdown_by_time 模块的全面测试，
包括正常情况、边界情况和异常情况的测试。
"""
import unittest
import os
import tempfile
import shutil
import time
from unittest.mock import patch, mock_open
import sys

# 导入被测试的模块
from combine_markdown_by_time import combine_markdown_files_by_time


class TestCombineMarkdownByTime(unittest.TestCase):
    """测试 combine_markdown_files_by_time 函数的测试类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建临时目录用于测试
        self.test_dir = tempfile.mkdtemp()
        self.output_file = os.path.join(self.test_dir, "test_output.md")
        
        # 创建测试用的markdown文件
        self.test_files = []
        
    def tearDown(self):
        """测试后的清理工作"""
        # 删除临时目录和所有测试文件
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content, delay=0.1):
        """
        创建测试文件的辅助方法
        
        Args:
            filename (str): 文件名
            content (str): 文件内容
            delay (float): 创建文件之间的延迟，确保创建时间不同
        """
        filepath = os.path.join(self.test_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        self.test_files.append(filename)
        
        # 添加延迟确保文件创建时间不同
        if delay > 0:
            time.sleep(delay)
        
        return filepath
    
    def test_normal_case_multiple_files(self):
        """测试正常情况：多个markdown文件按创建时间排序"""
        # 创建多个测试文件，按特定顺序创建
        self.create_test_file("third.md", "# 第三个文件\n这是第三个创建的文件。")
        self.create_test_file("first.md", "# 第一个文件\n这是第一个创建的文件。")
        self.create_test_file("second.md", "# 第二个文件\n这是第二个创建的文件。")
        
        # 执行合并
        with patch('builtins.print') as mock_print:
            combine_markdown_files_by_time(self.test_dir, self.output_file)
        
        # 验证输出文件是否存在
        self.assertTrue(os.path.exists(self.output_file))
        
        # 读取输出文件内容
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证文件内容包含所有源文件的内容
        self.assertIn("第三个文件", content)
        self.assertIn("第一个文件", content)
        self.assertIn("第二个文件", content)
        
        # 验证文件信息注释存在
        self.assertIn("<!-- 文件: third.md", content)
        self.assertIn("<!-- 文件: first.md", content)
        self.assertIn("<!-- 文件: second.md", content)
        
        # 验证分隔符存在
        self.assertIn("---", content)
        
        # 验证成功消息被打印
        mock_print.assert_any_call(f"成功将 3 个Markdown文件按创建时间顺序合并到 '{self.output_file}'")
    
    def test_single_file(self):
        """测试边界情况：只有一个markdown文件"""
        self.create_test_file("single.md", "# 单个文件\n这是唯一的文件。")
        
        with patch('builtins.print') as mock_print:
            combine_markdown_files_by_time(self.test_dir, self.output_file)
        
        # 验证输出文件存在
        self.assertTrue(os.path.exists(self.output_file))
        
        # 读取内容
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证内容正确
        self.assertIn("单个文件", content)
        self.assertIn("<!-- 文件: single.md", content)
        
        # 单个文件不应该有分隔符
        self.assertNotIn("---", content)
        
        # 验证成功消息
        mock_print.assert_any_call(f"成功将 1 个Markdown文件按创建时间顺序合并到 '{self.output_file}'")
    
    def test_empty_directory(self):
        """测试边界情况：空目录（没有markdown文件）"""
        # 创建一些非markdown文件
        self.create_test_file("readme.txt", "这不是markdown文件")
        self.create_test_file("config.json", '{"key": "value"}')
        
        with patch('builtins.print') as mock_print:
            combine_markdown_files_by_time(self.test_dir, self.output_file)
        
        # 验证输出文件不存在
        self.assertFalse(os.path.exists(self.output_file))
        
        # 验证错误消息被打印
        mock_print.assert_called_with(f"在目录 '{self.test_dir}' 中没有找到Markdown文件")
    
    def test_nonexistent_directory(self):
        """测试异常情况：不存在的目录"""
        nonexistent_dir = "/path/that/does/not/exist"
        
        with patch('builtins.print') as mock_print:
            combine_markdown_files_by_time(nonexistent_dir, self.output_file)
        
        # 验证输出文件不存在
        self.assertFalse(os.path.exists(self.output_file))
        
        # 验证错误消息被打印
        mock_print.assert_called_with(f"错误：目录 '{nonexistent_dir}' 不存在")
    
    def test_file_permission_error(self):
        """测试异常情况：文件权限问题"""
        # 创建一个测试文件
        test_file = self.create_test_file("test.md", "# 测试文件")
        
        # 模拟文件权限错误
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            with patch('builtins.print') as mock_print:
                combine_markdown_files_by_time(self.test_dir, self.output_file)
        
        # 验证错误消息被打印
        mock_print.assert_any_call(f"错误：无法创建输出文件 '{self.output_file}': Permission denied")
    
    def test_unicode_decode_error(self):
        """测试异常情况：文件编码错误"""
        # 创建一个包含非UTF-8编码的文件
        bad_file = os.path.join(self.test_dir, "bad_encoding.md")
        with open(bad_file, 'wb') as f:
            f.write(b'\xff\xfe# \x00\x4e\x2d\x65\x87')  # 写入无效的UTF-8字节
        
        # 创建一个正常的文件
        self.create_test_file("good.md", "# 正常文件")
        
        with patch('builtins.print') as mock_print:
            combine_markdown_files_by_time(self.test_dir, self.output_file)
        
        # 验证警告消息被打印
        mock_print.assert_any_call("警告：文件 'bad_encoding.md' 编码错误，跳过该文件: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte")
        
        # 验证正常文件仍然被处理
        self.assertTrue(os.path.exists(self.output_file))
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        self.assertIn("正常文件", content)
    
    def test_default_parameters(self):
        """测试默认参数"""
        # 在当前目录创建测试文件
        current_dir = os.getcwd()
        test_file = os.path.join(current_dir, "temp_test.md")
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("# 临时测试文件")
            
            # 使用默认参数调用函数
            with patch('builtins.print') as mock_print:
                combine_markdown_files_by_time()
            
            # 验证默认输出文件存在
            default_output = "combined_by_time.md"
            self.assertTrue(os.path.exists(default_output))
            
            # 清理
            if os.path.exists(default_output):
                os.remove(default_output)
                
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)


class TestTimeOrdering(unittest.TestCase):
    """专门测试时间排序功能的测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_dir = tempfile.mkdtemp()
        self.output_file = os.path.join(self.test_dir, "time_test_output.md")
    
    def tearDown(self):
        """测试后的清理工作"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_time_ordering_verification(self):
        """验证文件确实按创建时间排序"""
        # 创建文件，确保有明显的时间差
        files_data = [
            ("file_1_oldest.md", "# 最老的文件\n这是第一个创建的文件", 1.2),
            ("file_2_middle.md", "# 中间的文件\n这是第二个创建的文件", 1.2),
            ("file_3_newest.md", "# 最新的文件\n这是第三个创建的文件", 1.2)
        ]

        creation_times = {}
        for filename, content, delay in files_data:
            filepath = os.path.join(self.test_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            # 记录创建时间
            stat_info = os.stat(filepath)
            if hasattr(stat_info, 'st_birthtime'):
                creation_times[filename] = stat_info.st_birthtime
            else:
                creation_times[filename] = min(stat_info.st_ctime, stat_info.st_mtime)

            time.sleep(delay)

        # 验证创建时间确实不同
        times = list(creation_times.values())
        self.assertLess(times[0], times[1], "第一个文件应该比第二个文件创建得早")
        self.assertLess(times[1], times[2], "第二个文件应该比第三个文件创建得早")

        # 执行合并
        with patch('builtins.print'):
            combine_markdown_files_by_time(self.test_dir, self.output_file)

        # 读取输出文件
        with open(self.output_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 验证文件顺序：按创建时间排序
        oldest_pos = content.find("最老的文件")
        middle_pos = content.find("中间的文件")
        newest_pos = content.find("最新的文件")

        # 确保所有文件都被找到
        self.assertGreater(oldest_pos, -1, "最老的文件内容应该存在")
        self.assertGreater(middle_pos, -1, "中间的文件内容应该存在")
        self.assertGreater(newest_pos, -1, "最新的文件内容应该存在")

        # 验证顺序：最老的文件应该在最前面
        self.assertLess(oldest_pos, middle_pos, "最老的文件应该在中间文件之前")
        self.assertLess(middle_pos, newest_pos, "中间文件应该在最新文件之前")


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
